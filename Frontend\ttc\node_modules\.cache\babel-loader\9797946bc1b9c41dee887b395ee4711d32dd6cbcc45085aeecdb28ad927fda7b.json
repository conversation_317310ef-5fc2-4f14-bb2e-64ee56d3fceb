{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\MAIN_PRO\\\\SAGA\\\\TTC\\\\Frontend\\\\ttc\\\\src\\\\App.js\",\n  _s = $RefreshSig$();\nimport { useState } from 'react';\nimport './App.css';\nimport SideBar from './components/SideBar';\nimport ExpenditureForm from './components/ExpenditureForm';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [activeItem, setActiveItem] = useState('Dashboard');\n  const renderContent = () => {\n    switch (activeItem) {\n      case 'Dashboard':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-800 mb-4\",\n            children: \"Welcome to TTC Dashboard\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 14,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Select an option from the sidebar to get started.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-blue-50 border border-blue-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-blue-800\",\n                children: \"Expenditure\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 18,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-blue-600 text-sm\",\n                children: \"Manage your expenses\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 19,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 17,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-green-50 border border-green-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-green-800\",\n                children: \"Income\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 22,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-green-600 text-sm\",\n                children: \"Track your income\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 21,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"bg-gray-50 border border-gray-200 rounded-lg p-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-lg font-semibold text-gray-800\",\n                children: \"Settings\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-gray-600 text-sm\",\n                children: \"Configure your preferences\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 16,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 13,\n          columnNumber: 11\n        }, this);\n      case 'Expenditure':\n        return /*#__PURE__*/_jsxDEV(ExpenditureForm, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 16\n        }, this);\n      case 'Income':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-800 mb-4\",\n            children: \"Income Management\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 37,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Income tracking feature coming soon...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 11\n        }, this);\n      case 'Settings':\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-800 mb-4\",\n            children: \"Settings\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Settings panel coming soon...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 45,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"bg-white rounded-lg shadow-md p-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n            className: \"text-3xl font-bold text-gray-800 mb-4\",\n            children: \"Welcome to TTC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600\",\n            children: \"Select an option from the sidebar to get started.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex min-h-screen bg-gray-100\",\n    children: [/*#__PURE__*/_jsxDEV(SideBar, {\n      activeItem: activeItem,\n      setActiveItem: setActiveItem\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 61,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 p-8\",\n      children: renderContent()\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 64,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"5eEapdT6qbyDXwn8xouVXcHb0Aw=\");\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["useState", "SideBar", "ExpenditureForm", "jsxDEV", "_jsxDEV", "App", "_s", "activeItem", "setActiveItem", "renderContent", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/MAIN_PRO/SAGA/TTC/Frontend/ttc/src/App.js"], "sourcesContent": ["import { useState } from 'react';\nimport './App.css';\nimport SideBar from './components/SideBar';\nimport ExpenditureForm from './components/ExpenditureForm';\n\nfunction App() {\n  const [activeItem, setActiveItem] = useState('Dashboard');\n\n  const renderContent = () => {\n    switch (activeItem) {\n      case 'Dashboard':\n        return (\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">Welcome to TTC Dashboard</h1>\n            <p className=\"text-gray-600\">Select an option from the sidebar to get started.</p>\n            <div className=\"mt-6 grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                <h3 className=\"text-lg font-semibold text-blue-800\">Expenditure</h3>\n                <p className=\"text-blue-600 text-sm\">Manage your expenses</p>\n              </div>\n              <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n                <h3 className=\"text-lg font-semibold text-green-800\">Income</h3>\n                <p className=\"text-green-600 text-sm\">Track your income</p>\n              </div>\n              <div className=\"bg-gray-50 border border-gray-200 rounded-lg p-4\">\n                <h3 className=\"text-lg font-semibold text-gray-800\">Settings</h3>\n                <p className=\"text-gray-600 text-sm\">Configure your preferences</p>\n              </div>\n            </div>\n          </div>\n        );\n      case 'Expenditure':\n        return <ExpenditureForm />;\n      case 'Income':\n        return (\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">Income Management</h1>\n            <p className=\"text-gray-600\">Income tracking feature coming soon...</p>\n          </div>\n        );\n      case 'Settings':\n        return (\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">Settings</h1>\n            <p className=\"text-gray-600\">Settings panel coming soon...</p>\n          </div>\n        );\n      default:\n        return (\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h1 className=\"text-3xl font-bold text-gray-800 mb-4\">Welcome to TTC</h1>\n            <p className=\"text-gray-600\">Select an option from the sidebar to get started.</p>\n          </div>\n        );\n    }\n  };\n\n  return (\n    <div className=\"flex min-h-screen bg-gray-100\">\n      {/* Sidebar */}\n      <SideBar activeItem={activeItem} setActiveItem={setActiveItem} />\n\n      {/* Main Content */}\n      <div className=\"flex-1 p-8\">\n        {renderContent()}\n      </div>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAO,WAAW;AAClB,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,eAAe,MAAM,8BAA8B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,WAAW,CAAC;EAEzD,MAAMS,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQF,UAAU;MAChB,KAAK,WAAW;QACd,oBACEH,OAAA;UAAKM,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDP,OAAA;YAAIM,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAwB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnFX,OAAA;YAAGM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eAClFX,OAAA;YAAKM,SAAS,EAAC,4CAA4C;YAAAC,QAAA,gBACzDP,OAAA;cAAKM,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC/DP,OAAA;gBAAIM,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACpEX,OAAA;gBAAGM,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAoB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1D,CAAC,eACNX,OAAA;cAAKM,SAAS,EAAC,oDAAoD;cAAAC,QAAA,gBACjEP,OAAA;gBAAIM,SAAS,EAAC,sCAAsC;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChEX,OAAA;gBAAGM,SAAS,EAAC,wBAAwB;gBAAAC,QAAA,EAAC;cAAiB;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC,eACNX,OAAA;cAAKM,SAAS,EAAC,kDAAkD;cAAAC,QAAA,gBAC/DP,OAAA;gBAAIM,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjEX,OAAA;gBAAGM,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAA0B;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAEV,KAAK,aAAa;QAChB,oBAAOX,OAAA,CAACF,eAAe;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAC5B,KAAK,QAAQ;QACX,oBACEX,OAAA;UAAKM,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDP,OAAA;YAAIM,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAiB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAC5EX,OAAA;YAAGM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAsC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpE,CAAC;MAEV,KAAK,UAAU;QACb,oBACEX,OAAA;UAAKM,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDP,OAAA;YAAIM,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACnEX,OAAA;YAAGM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAA6B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3D,CAAC;MAEV;QACE,oBACEX,OAAA;UAAKM,SAAS,EAAC,mCAAmC;UAAAC,QAAA,gBAChDP,OAAA;YAAIM,SAAS,EAAC,uCAAuC;YAAAC,QAAA,EAAC;UAAc;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eACzEX,OAAA;YAAGM,SAAS,EAAC,eAAe;YAAAC,QAAA,EAAC;UAAiD;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC/E,CAAC;IAEZ;EACF,CAAC;EAED,oBACEX,OAAA;IAAKM,SAAS,EAAC,+BAA+B;IAAAC,QAAA,gBAE5CP,OAAA,CAACH,OAAO;MAACM,UAAU,EAAEA,UAAW;MAACC,aAAa,EAAEA;IAAc;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGjEX,OAAA;MAAKM,SAAS,EAAC,YAAY;MAAAC,QAAA,EACxBF,aAAa,CAAC;IAAC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACb,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAACT,EAAA,CA/DQD,GAAG;AAAAW,EAAA,GAAHX,GAAG;AAiEZ,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}