import { useState } from 'react';
import './App.css';
import SideBar from './components/SideBar';
import ExpenditureForm from './components/ExpenditureForm';

function App() {
  const [activeItem, setActiveItem] = useState('Dashboard');

  const renderContent = () => {
    switch (activeItem) {
      case 'Dashboard':
        return (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">Welcome to TTC Dashboard</h1>
            <p className="text-gray-600">Select an option from the sidebar to get started.</p>
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-blue-800">Expenditure</h3>
                <p className="text-blue-600 text-sm">Manage your expenses</p>
              </div>
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-green-800">Income</h3>
                <p className="text-green-600 text-sm">Track your income</p>
              </div>
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="text-lg font-semibold text-gray-800">Settings</h3>
                <p className="text-gray-600 text-sm">Configure your preferences</p>
              </div>
            </div>
          </div>
        );
      case 'Expenditure':
        return <ExpenditureForm />;
      case 'Income':
        return (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">Income Management</h1>
            <p className="text-gray-600">Income tracking feature coming soon...</p>
          </div>
        );
      case 'Settings':
        return (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">Settings</h1>
            <p className="text-gray-600">Settings panel coming soon...</p>
          </div>
        );
      default:
        return (
          <div className="bg-white rounded-lg shadow-md p-6">
            <h1 className="text-3xl font-bold text-gray-800 mb-4">Welcome to TTC</h1>
            <p className="text-gray-600">Select an option from the sidebar to get started.</p>
          </div>
        );
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-100">
      {/* Sidebar */}
      <SideBar activeItem={activeItem} setActiveItem={setActiveItem} />

      {/* Main Content */}
      <div className="flex-1 p-8">
        {renderContent()}
      </div>
    </div>
  );
}

export default App;
