{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\MAIN_PRO\\\\SAGA\\\\TTC\\\\Frontend\\\\ttc\\\\src\\\\components\\\\SideBar.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SideBar = ({\n  activeItem,\n  setActiveItem\n}) => {\n  const menuItems = [{\n    name: 'Dashboard',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 11,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 9\n    }, this)\n  }, {\n    name: 'Expenditure',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 9\n    }, this)\n  }, {\n    name: 'Income',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 9\n    }, this)\n  }, {\n    name: 'Settings',\n    icon: /*#__PURE__*/_jsxDEV(\"svg\", {\n      className: \"w-6 h-6\",\n      fill: \"none\",\n      stroke: \"currentColor\",\n      viewBox: \"0 0 24 24\",\n      children: [/*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        strokeWidth: 2,\n        d: \"M15 12a3 3 0 11-6 0 3 3 0 016 0z\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 9\n    }, this)\n  }];\n  const handleItemClick = itemName => {\n    setActiveItem(itemName);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gray-900 text-white w-64 min-h-screen p-4\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mb-8\",\n      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-2xl font-bold text-center\",\n        children: \"TTC\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"space-y-2\",\n      children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: () => handleItemClick(item.name),\n        className: `w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200 ${activeItem === item.name ? 'bg-blue-600 text-white' : 'text-gray-300 hover:bg-gray-800 hover:text-white'}`,\n        children: [item.icon, /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"font-medium\",\n          children: item.name\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this)]\n      }, item.name, true, {\n        fileName: _jsxFileName,\n        lineNumber: 56,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-4 left-4 right-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-xs text-gray-400 text-center\",\n        children: \"\\xA9 2024 TTC App\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 73,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 47,\n    columnNumber: 5\n  }, this);\n};\n_c = SideBar;\nexport default SideBar;\nvar _c;\n$RefreshReg$(_c, \"SideBar\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "SideBar", "activeItem", "setActiveItem", "menuItems", "name", "icon", "className", "fill", "stroke", "viewBox", "children", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleItemClick", "itemName", "map", "item", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/MAIN_PRO/SAGA/TTC/Frontend/ttc/src/components/SideBar.js"], "sourcesContent": ["import React from 'react'\r\n\r\nconst SideBar = ({ activeItem, setActiveItem }) => {\r\n\r\n  const menuItems = [\r\n    {\r\n      name: 'Dashboard',\r\n      icon: (\r\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\" />\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      name: 'Expenditure',\r\n      icon: (\r\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      name: 'Income',\r\n      icon: (\r\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\r\n        </svg>\r\n      )\r\n    },\r\n    {\r\n      name: 'Settings',\r\n      icon: (\r\n        <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\r\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\r\n        </svg>\r\n      )\r\n    }\r\n  ]\r\n\r\n  const handleItemClick = (itemName) => {\r\n    setActiveItem(itemName)\r\n  }\r\n\r\n  return (\r\n    <div className=\"bg-gray-900 text-white w-64 min-h-screen p-4\">\r\n      {/* Logo/Title */}\r\n      <div className=\"mb-8\">\r\n        <h1 className=\"text-2xl font-bold text-center\">TTC</h1>\r\n      </div>\r\n\r\n      {/* Navigation Menu */}\r\n      <nav className=\"space-y-2\">\r\n        {menuItems.map((item) => (\r\n          <button\r\n            key={item.name}\r\n            onClick={() => handleItemClick(item.name)}\r\n            className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-colors duration-200 ${\r\n              activeItem === item.name\r\n                ? 'bg-blue-600 text-white'\r\n                : 'text-gray-300 hover:bg-gray-800 hover:text-white'\r\n            }`}\r\n          >\r\n            {item.icon}\r\n            <span className=\"font-medium\">{item.name}</span>\r\n          </button>\r\n        ))}\r\n      </nav>\r\n\r\n      {/* Footer */}\r\n      <div className=\"absolute bottom-4 left-4 right-4\">\r\n        <div className=\"text-xs text-gray-400 text-center\">\r\n          © 2024 TTC App\r\n        </div>\r\n      </div>\r\n    </div>\r\n  )\r\n}\r\n\r\nexport default SideBar"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEzB,MAAMC,OAAO,GAAGA,CAAC;EAAEC,UAAU;EAAEC;AAAc,CAAC,KAAK;EAEjD,MAAMC,SAAS,GAAG,CAChB;IACEC,IAAI,EAAE,WAAW;IACjBC,IAAI,eACFN,OAAA;MAAKO,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,gBAC5EX,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAmE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC3InB,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAkE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvI;EAET,CAAC,EACD;IACEd,IAAI,EAAE,aAAa;IACnBC,IAAI,eACFN,OAAA;MAAKO,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC5EX,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAoJ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACzN;EAET,CAAC,EACD;IACEd,IAAI,EAAE,QAAQ;IACdC,IAAI,eACFN,OAAA;MAAKO,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,eAC5EX,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAA2I;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAChN;EAET,CAAC,EACD;IACEd,IAAI,EAAE,UAAU;IAChBC,IAAI,eACFN,OAAA;MAAKO,SAAS,EAAC,SAAS;MAACC,IAAI,EAAC,MAAM;MAACC,MAAM,EAAC,cAAc;MAACC,OAAO,EAAC,WAAW;MAAAC,QAAA,gBAC5EX,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAqe;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC7iBnB,OAAA;QAAMY,aAAa,EAAC,OAAO;QAACC,cAAc,EAAC,OAAO;QAACC,WAAW,EAAE,CAAE;QAACC,CAAC,EAAC;MAAkC;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvG;EAET,CAAC,CACF;EAED,MAAMC,eAAe,GAAIC,QAAQ,IAAK;IACpClB,aAAa,CAACkB,QAAQ,CAAC;EACzB,CAAC;EAED,oBACErB,OAAA;IAAKO,SAAS,EAAC,8CAA8C;IAAAI,QAAA,gBAE3DX,OAAA;MAAKO,SAAS,EAAC,MAAM;MAAAI,QAAA,eACnBX,OAAA;QAAIO,SAAS,EAAC,gCAAgC;QAAAI,QAAA,EAAC;MAAG;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD,CAAC,eAGNnB,OAAA;MAAKO,SAAS,EAAC,WAAW;MAAAI,QAAA,EACvBP,SAAS,CAACkB,GAAG,CAAEC,IAAI,iBAClBvB,OAAA;QAEEwB,OAAO,EAAEA,CAAA,KAAMJ,eAAe,CAACG,IAAI,CAAClB,IAAI,CAAE;QAC1CE,SAAS,EAAE,0FACTL,UAAU,KAAKqB,IAAI,CAAClB,IAAI,GACpB,wBAAwB,GACxB,kDAAkD,EACrD;QAAAM,QAAA,GAEFY,IAAI,CAACjB,IAAI,eACVN,OAAA;UAAMO,SAAS,EAAC,aAAa;UAAAI,QAAA,EAAEY,IAAI,CAAClB;QAAI;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA,GAT3CI,IAAI,CAAClB,IAAI;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAUR,CACT;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC,eAGNnB,OAAA;MAAKO,SAAS,EAAC,kCAAkC;MAAAI,QAAA,eAC/CX,OAAA;QAAKO,SAAS,EAAC,mCAAmC;QAAAI,QAAA,EAAC;MAEnD;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAAM,EAAA,GA5EKxB,OAAO;AA8Eb,eAAeA,OAAO;AAAA,IAAAwB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}