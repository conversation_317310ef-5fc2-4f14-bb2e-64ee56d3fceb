import React, { useState } from 'react'

const ExpenditureForm = () => {
  const [formData, setFormData] = useState({
    assigneeName: '',
    productName: '',
    productPrice: ''
  })
  
  const [expenditures, setExpenditures] = useState([])
  const [errors, setErrors] = useState({})

  // Calculate total of all expenditures
  const totalAmount = expenditures.reduce((sum, item) => sum + parseFloat(item.productPrice || 0), 0)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}
    
    if (!formData.assigneeName.trim()) {
      newErrors.assigneeName = 'Assignee name is required'
    }
    
    if (!formData.productName.trim()) {
      newErrors.productName = 'Product name is required'
    }
    
    if (!formData.productPrice.trim()) {
      newErrors.productPrice = 'Product price is required'
    } else if (isNaN(formData.productPrice) || parseFloat(formData.productPrice) <= 0) {
      newErrors.productPrice = 'Please enter a valid price greater than 0'
    }
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (validateForm()) {
      const newExpenditure = {
        id: Date.now(), // Simple ID generation
        assigneeName: formData.assigneeName.trim(),
        productName: formData.productName.trim(),
        productPrice: parseFloat(formData.productPrice).toFixed(2),
        date: new Date().toLocaleDateString()
      }
      
      setExpenditures(prev => [...prev, newExpenditure])
      
      // Reset form
      setFormData({
        assigneeName: '',
        productName: '',
        productPrice: ''
      })
    }
  }

  const handleDelete = (id) => {
    setExpenditures(prev => prev.filter(item => item.id !== id))
  }

  return (
    <div className="space-y-6">
      {/* Header with Total */}
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold text-gray-800">Expenditure Management</h1>
        <div className="bg-blue-100 border border-blue-300 rounded-lg px-6 py-3">
          <div className="text-sm text-blue-600 font-medium">Total Expenditure</div>
          <div className="text-2xl font-bold text-blue-800">${totalAmount.toFixed(2)}</div>
        </div>
      </div>

      {/* Expenditure Form */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-xl font-semibold text-gray-800 mb-4">Add New Expenditure</h2>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Assignee Name */}
            <div>
              <label htmlFor="assigneeName" className="block text-sm font-medium text-gray-700 mb-1">
                Assignee Name *
              </label>
              <input
                type="text"
                id="assigneeName"
                name="assigneeName"
                value={formData.assigneeName}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.assigneeName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter assignee name"
              />
              {errors.assigneeName && (
                <p className="text-red-500 text-xs mt-1">{errors.assigneeName}</p>
              )}
            </div>

            {/* Product Name */}
            <div>
              <label htmlFor="productName" className="block text-sm font-medium text-gray-700 mb-1">
                Product Name *
              </label>
              <input
                type="text"
                id="productName"
                name="productName"
                value={formData.productName}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.productName ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Enter product name"
              />
              {errors.productName && (
                <p className="text-red-500 text-xs mt-1">{errors.productName}</p>
              )}
            </div>

            {/* Product Price */}
            <div>
              <label htmlFor="productPrice" className="block text-sm font-medium text-gray-700 mb-1">
                Product Price * ($)
              </label>
              <input
                type="number"
                id="productPrice"
                name="productPrice"
                value={formData.productPrice}
                onChange={handleInputChange}
                step="0.01"
                min="0"
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                  errors.productPrice ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="0.00"
              />
              {errors.productPrice && (
                <p className="text-red-500 text-xs mt-1">{errors.productPrice}</p>
              )}
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-200"
            >
              Add Expenditure
            </button>
          </div>
        </form>
      </div>

      {/* Expenditures List */}
      {expenditures.length > 0 && (
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">Expenditure History</h2>
          
          <div className="overflow-x-auto">
            <table className="w-full table-auto">
              <thead>
                <tr className="bg-gray-50">
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Date</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Assignee</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Product</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Price</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-700">Action</th>
                </tr>
              </thead>
              <tbody>
                {expenditures.map((item) => (
                  <tr key={item.id} className="border-t border-gray-200 hover:bg-gray-50">
                    <td className="px-4 py-2 text-sm text-gray-600">{item.date}</td>
                    <td className="px-4 py-2 text-sm text-gray-800">{item.assigneeName}</td>
                    <td className="px-4 py-2 text-sm text-gray-800">{item.productName}</td>
                    <td className="px-4 py-2 text-sm text-gray-800 font-medium">${item.productPrice}</td>
                    <td className="px-4 py-2">
                      <button
                        onClick={() => handleDelete(item.id)}
                        className="text-red-600 hover:text-red-800 text-sm font-medium"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  )
}

export default ExpenditureForm
