{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\MAIN_PRO\\\\SAGA\\\\TTC\\\\Frontend\\\\ttc\\\\src\\\\components\\\\IncomeForm.js\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst IncomeForm = () => {\n  _s();\n  const [formData, setFormData] = useState({\n    assigneeName: '',\n    productName: '',\n    productPrice: ''\n  });\n  const [incomes, setIncomes] = useState([]);\n  const [errors, setErrors] = useState({});\n\n  // Calculate total of all incomes\n  const totalAmount = incomes.reduce((sum, item) => sum + parseFloat(item.productPrice || 0), 0);\n  const handleInputChange = e => {\n    const {\n      name,\n      value\n    } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n\n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }));\n    }\n  };\n  const validateForm = () => {\n    const newErrors = {};\n    if (!formData.assigneeName.trim()) {\n      newErrors.assigneeName = 'Assignee name is required';\n    }\n    if (!formData.productName.trim()) {\n      newErrors.productName = 'Product name is required';\n    }\n    if (!formData.productPrice.trim()) {\n      newErrors.productPrice = 'Product price is required';\n    } else if (isNaN(formData.productPrice) || parseFloat(formData.productPrice) <= 0) {\n      newErrors.productPrice = 'Please enter a valid price greater than 0';\n    }\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n  const handleSubmit = e => {\n    e.preventDefault();\n    if (validateForm()) {\n      const newIncome = {\n        id: Date.now(),\n        // Simple ID generation\n        assigneeName: formData.assigneeName.trim(),\n        productName: formData.productName.trim(),\n        productPrice: parseFloat(formData.productPrice).toFixed(2),\n        date: new Date().toLocaleDateString()\n      };\n      setIncomes(prev => [...prev, newIncome]);\n\n      // Reset form\n      setFormData({\n        assigneeName: '',\n        productName: '',\n        productPrice: ''\n      });\n    }\n  };\n  const handleDelete = id => {\n    setIncomes(prev => prev.filter(item => item.id !== id));\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"space-y-6\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex justify-between items-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n        className: \"text-3xl font-bold text-gray-800\",\n        children: \"Income Management\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-green-100 border border-green-300 rounded-lg px-6 py-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-green-600 font-medium\",\n          children: \"Total Income\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-green-800\",\n          children: [\"$\", totalAmount.toFixed(2)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 83,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-800 mb-4\",\n        children: \"Add New Income\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 93,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"assigneeName\",\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Assignee Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 99,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"assigneeName\",\n              name: \"assigneeName\",\n              value: formData.assigneeName,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${errors.assigneeName ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"Enter assignee name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), errors.assigneeName && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.assigneeName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 114,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 98,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"productName\",\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Product Name *\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 120,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              id: \"productName\",\n              name: \"productName\",\n              value: formData.productName,\n              onChange: handleInputChange,\n              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${errors.productName ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"Enter product name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 123,\n              columnNumber: 15\n            }, this), errors.productName && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.productName\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"label\", {\n              htmlFor: \"productPrice\",\n              className: \"block text-sm font-medium text-gray-700 mb-1\",\n              children: \"Product Price * ($)\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"number\",\n              id: \"productPrice\",\n              name: \"productPrice\",\n              value: formData.productPrice,\n              onChange: handleInputChange,\n              step: \"0.01\",\n              min: \"0\",\n              className: `w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${errors.productPrice ? 'border-red-500' : 'border-gray-300'}`,\n              placeholder: \"0.00\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this), errors.productPrice && /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-red-500 text-xs mt-1\",\n              children: errors.productPrice\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 158,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-end\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            type: \"submit\",\n            className: \"bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors duration-200\",\n            children: \"Add Income\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 165,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 164,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 92,\n      columnNumber: 7\n    }, this), incomes.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"bg-white rounded-lg shadow-md p-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n        className: \"text-xl font-semibold text-gray-800 mb-4\",\n        children: \"Income History\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"overflow-x-auto\",\n        children: /*#__PURE__*/_jsxDEV(\"table\", {\n          className: \"w-full table-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n            children: /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-2 text-left text-sm font-medium text-gray-700\",\n                children: \"Date\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 184,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-2 text-left text-sm font-medium text-gray-700\",\n                children: \"Assignee\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 185,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-2 text-left text-sm font-medium text-gray-700\",\n                children: \"Product\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 186,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-2 text-left text-sm font-medium text-gray-700\",\n                children: \"Price\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 187,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n                className: \"px-4 py-2 text-left text-sm font-medium text-gray-700\",\n                children: \"Action\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 183,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n            children: incomes.map(item => /*#__PURE__*/_jsxDEV(\"tr\", {\n              className: \"border-t border-gray-200 hover:bg-gray-50\",\n              children: [/*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-2 text-sm text-gray-600\",\n                children: item.date\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 194,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-2 text-sm text-gray-800\",\n                children: item.assigneeName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 195,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-2 text-sm text-gray-800\",\n                children: item.productName\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 196,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-2 text-sm text-gray-800 font-medium\",\n                children: [\"$\", item.productPrice]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 197,\n                columnNumber: 21\n              }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n                className: \"px-4 py-2\",\n                children: /*#__PURE__*/_jsxDEV(\"button\", {\n                  onClick: () => handleDelete(item.id),\n                  className: \"text-red-600 hover:text-red-800 text-sm font-medium\",\n                  children: \"Delete\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 199,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 198,\n                columnNumber: 21\n              }, this)]\n            }, item.id, true, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this))\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 191,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 180,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 5\n  }, this);\n};\n_s(IncomeForm, \"XlWVE9EvHSL5nFfXSGdn9OZrqiQ=\");\n_c = IncomeForm;\nexport default IncomeForm;\nvar _c;\n$RefreshReg$(_c, \"IncomeForm\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "IncomeForm", "_s", "formData", "setFormData", "assignee<PERSON>ame", "productName", "productPrice", "incomes", "<PERSON><PERSON><PERSON><PERSON>", "errors", "setErrors", "totalAmount", "reduce", "sum", "item", "parseFloat", "handleInputChange", "e", "name", "value", "target", "prev", "validateForm", "newErrors", "trim", "isNaN", "Object", "keys", "length", "handleSubmit", "preventDefault", "newIncome", "id", "Date", "now", "toFixed", "date", "toLocaleDateString", "handleDelete", "filter", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onSubmit", "htmlFor", "type", "onChange", "placeholder", "step", "min", "map", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Downloads/MAIN_PRO/SAGA/TTC/Frontend/ttc/src/components/IncomeForm.js"], "sourcesContent": ["import React, { useState } from 'react'\n\nconst IncomeForm = () => {\n  const [formData, setFormData] = useState({\n    assigneeName: '',\n    productName: '',\n    productPrice: ''\n  })\n  \n  const [incomes, setIncomes] = useState([])\n  const [errors, setErrors] = useState({})\n\n  // Calculate total of all incomes\n  const totalAmount = incomes.reduce((sum, item) => sum + parseFloat(item.productPrice || 0), 0)\n\n  const handleInputChange = (e) => {\n    const { name, value } = e.target\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }))\n    \n    // Clear error when user starts typing\n    if (errors[name]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: ''\n      }))\n    }\n  }\n\n  const validateForm = () => {\n    const newErrors = {}\n    \n    if (!formData.assigneeName.trim()) {\n      newErrors.assigneeName = 'Assignee name is required'\n    }\n    \n    if (!formData.productName.trim()) {\n      newErrors.productName = 'Product name is required'\n    }\n    \n    if (!formData.productPrice.trim()) {\n      newErrors.productPrice = 'Product price is required'\n    } else if (isNaN(formData.productPrice) || parseFloat(formData.productPrice) <= 0) {\n      newErrors.productPrice = 'Please enter a valid price greater than 0'\n    }\n    \n    setErrors(newErrors)\n    return Object.keys(newErrors).length === 0\n  }\n\n  const handleSubmit = (e) => {\n    e.preventDefault()\n    \n    if (validateForm()) {\n      const newIncome = {\n        id: Date.now(), // Simple ID generation\n        assigneeName: formData.assigneeName.trim(),\n        productName: formData.productName.trim(),\n        productPrice: parseFloat(formData.productPrice).toFixed(2),\n        date: new Date().toLocaleDateString()\n      }\n      \n      setIncomes(prev => [...prev, newIncome])\n      \n      // Reset form\n      setFormData({\n        assigneeName: '',\n        productName: '',\n        productPrice: ''\n      })\n    }\n  }\n\n  const handleDelete = (id) => {\n    setIncomes(prev => prev.filter(item => item.id !== id))\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header with Total */}\n      <div className=\"flex justify-between items-center\">\n        <h1 className=\"text-3xl font-bold text-gray-800\">Income Management</h1>\n        <div className=\"bg-green-100 border border-green-300 rounded-lg px-6 py-3\">\n          <div className=\"text-sm text-green-600 font-medium\">Total Income</div>\n          <div className=\"text-2xl font-bold text-green-800\">${totalAmount.toFixed(2)}</div>\n        </div>\n      </div>\n\n      {/* Income Form */}\n      <div className=\"bg-white rounded-lg shadow-md p-6\">\n        <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Add New Income</h2>\n        \n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            {/* Assignee Name */}\n            <div>\n              <label htmlFor=\"assigneeName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Assignee Name *\n              </label>\n              <input\n                type=\"text\"\n                id=\"assigneeName\"\n                name=\"assigneeName\"\n                value={formData.assigneeName}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${\n                  errors.assigneeName ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"Enter assignee name\"\n              />\n              {errors.assigneeName && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.assigneeName}</p>\n              )}\n            </div>\n\n            {/* Product Name */}\n            <div>\n              <label htmlFor=\"productName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Product Name *\n              </label>\n              <input\n                type=\"text\"\n                id=\"productName\"\n                name=\"productName\"\n                value={formData.productName}\n                onChange={handleInputChange}\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${\n                  errors.productName ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"Enter product name\"\n              />\n              {errors.productName && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.productName}</p>\n              )}\n            </div>\n\n            {/* Product Price */}\n            <div>\n              <label htmlFor=\"productPrice\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n                Product Price * ($)\n              </label>\n              <input\n                type=\"number\"\n                id=\"productPrice\"\n                name=\"productPrice\"\n                value={formData.productPrice}\n                onChange={handleInputChange}\n                step=\"0.01\"\n                min=\"0\"\n                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 ${\n                  errors.productPrice ? 'border-red-500' : 'border-gray-300'\n                }`}\n                placeholder=\"0.00\"\n              />\n              {errors.productPrice && (\n                <p className=\"text-red-500 text-xs mt-1\">{errors.productPrice}</p>\n              )}\n            </div>\n          </div>\n\n          {/* Submit Button */}\n          <div className=\"flex justify-end\">\n            <button\n              type=\"submit\"\n              className=\"bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 transition-colors duration-200\"\n            >\n              Add Income\n            </button>\n          </div>\n        </form>\n      </div>\n\n      {/* Incomes List */}\n      {incomes.length > 0 && (\n        <div className=\"bg-white rounded-lg shadow-md p-6\">\n          <h2 className=\"text-xl font-semibold text-gray-800 mb-4\">Income History</h2>\n          \n          <div className=\"overflow-x-auto\">\n            <table className=\"w-full table-auto\">\n              <thead>\n                <tr className=\"bg-gray-50\">\n                  <th className=\"px-4 py-2 text-left text-sm font-medium text-gray-700\">Date</th>\n                  <th className=\"px-4 py-2 text-left text-sm font-medium text-gray-700\">Assignee</th>\n                  <th className=\"px-4 py-2 text-left text-sm font-medium text-gray-700\">Product</th>\n                  <th className=\"px-4 py-2 text-left text-sm font-medium text-gray-700\">Price</th>\n                  <th className=\"px-4 py-2 text-left text-sm font-medium text-gray-700\">Action</th>\n                </tr>\n              </thead>\n              <tbody>\n                {incomes.map((item) => (\n                  <tr key={item.id} className=\"border-t border-gray-200 hover:bg-gray-50\">\n                    <td className=\"px-4 py-2 text-sm text-gray-600\">{item.date}</td>\n                    <td className=\"px-4 py-2 text-sm text-gray-800\">{item.assigneeName}</td>\n                    <td className=\"px-4 py-2 text-sm text-gray-800\">{item.productName}</td>\n                    <td className=\"px-4 py-2 text-sm text-gray-800 font-medium\">${item.productPrice}</td>\n                    <td className=\"px-4 py-2\">\n                      <button\n                        onClick={() => handleDelete(item.id)}\n                        className=\"text-red-600 hover:text-red-800 text-sm font-medium\"\n                      >\n                        Delete\n                      </button>\n                    </td>\n                  </tr>\n                ))}\n              </tbody>\n            </table>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default IncomeForm\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,UAAU,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGN,QAAQ,CAAC;IACvCO,YAAY,EAAE,EAAE;IAChBC,WAAW,EAAE,EAAE;IACfC,YAAY,EAAE;EAChB,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGX,QAAQ,CAAC,EAAE,CAAC;EAC1C,MAAM,CAACY,MAAM,EAAEC,SAAS,CAAC,GAAGb,QAAQ,CAAC,CAAC,CAAC,CAAC;;EAExC;EACA,MAAMc,WAAW,GAAGJ,OAAO,CAACK,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGE,UAAU,CAACD,IAAI,CAACR,YAAY,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;EAE9F,MAAMU,iBAAiB,GAAIC,CAAC,IAAK;IAC/B,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAGF,CAAC,CAACG,MAAM;IAChCjB,WAAW,CAACkB,IAAI,KAAK;MACnB,GAAGA,IAAI;MACP,CAACH,IAAI,GAAGC;IACV,CAAC,CAAC,CAAC;;IAEH;IACA,IAAIV,MAAM,CAACS,IAAI,CAAC,EAAE;MAChBR,SAAS,CAACW,IAAI,KAAK;QACjB,GAAGA,IAAI;QACP,CAACH,IAAI,GAAG;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EAED,MAAMI,YAAY,GAAGA,CAAA,KAAM;IACzB,MAAMC,SAAS,GAAG,CAAC,CAAC;IAEpB,IAAI,CAACrB,QAAQ,CAACE,YAAY,CAACoB,IAAI,CAAC,CAAC,EAAE;MACjCD,SAAS,CAACnB,YAAY,GAAG,2BAA2B;IACtD;IAEA,IAAI,CAACF,QAAQ,CAACG,WAAW,CAACmB,IAAI,CAAC,CAAC,EAAE;MAChCD,SAAS,CAAClB,WAAW,GAAG,0BAA0B;IACpD;IAEA,IAAI,CAACH,QAAQ,CAACI,YAAY,CAACkB,IAAI,CAAC,CAAC,EAAE;MACjCD,SAAS,CAACjB,YAAY,GAAG,2BAA2B;IACtD,CAAC,MAAM,IAAImB,KAAK,CAACvB,QAAQ,CAACI,YAAY,CAAC,IAAIS,UAAU,CAACb,QAAQ,CAACI,YAAY,CAAC,IAAI,CAAC,EAAE;MACjFiB,SAAS,CAACjB,YAAY,GAAG,2CAA2C;IACtE;IAEAI,SAAS,CAACa,SAAS,CAAC;IACpB,OAAOG,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC,CAACK,MAAM,KAAK,CAAC;EAC5C,CAAC;EAED,MAAMC,YAAY,GAAIZ,CAAC,IAAK;IAC1BA,CAAC,CAACa,cAAc,CAAC,CAAC;IAElB,IAAIR,YAAY,CAAC,CAAC,EAAE;MAClB,MAAMS,SAAS,GAAG;QAChBC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC;QAAE;QAChB9B,YAAY,EAAEF,QAAQ,CAACE,YAAY,CAACoB,IAAI,CAAC,CAAC;QAC1CnB,WAAW,EAAEH,QAAQ,CAACG,WAAW,CAACmB,IAAI,CAAC,CAAC;QACxClB,YAAY,EAAES,UAAU,CAACb,QAAQ,CAACI,YAAY,CAAC,CAAC6B,OAAO,CAAC,CAAC,CAAC;QAC1DC,IAAI,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,kBAAkB,CAAC;MACtC,CAAC;MAED7B,UAAU,CAACa,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAEU,SAAS,CAAC,CAAC;;MAExC;MACA5B,WAAW,CAAC;QACVC,YAAY,EAAE,EAAE;QAChBC,WAAW,EAAE,EAAE;QACfC,YAAY,EAAE;MAChB,CAAC,CAAC;IACJ;EACF,CAAC;EAED,MAAMgC,YAAY,GAAIN,EAAE,IAAK;IAC3BxB,UAAU,CAACa,IAAI,IAAIA,IAAI,CAACkB,MAAM,CAACzB,IAAI,IAAIA,IAAI,CAACkB,EAAE,KAAKA,EAAE,CAAC,CAAC;EACzD,CAAC;EAED,oBACEjC,OAAA;IAAKyC,SAAS,EAAC,WAAW;IAAAC,QAAA,gBAExB1C,OAAA;MAAKyC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD1C,OAAA;QAAIyC,SAAS,EAAC,kCAAkC;QAAAC,QAAA,EAAC;MAAiB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eACvE9C,OAAA;QAAKyC,SAAS,EAAC,2DAA2D;QAAAC,QAAA,gBACxE1C,OAAA;UAAKyC,SAAS,EAAC,oCAAoC;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC,eACtE9C,OAAA;UAAKyC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,GAAC,GAAC,EAAC9B,WAAW,CAACwB,OAAO,CAAC,CAAC,CAAC;QAAA;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN9C,OAAA;MAAKyC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD1C,OAAA;QAAIyC,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE5E9C,OAAA;QAAM+C,QAAQ,EAAEjB,YAAa;QAACW,SAAS,EAAC,WAAW;QAAAC,QAAA,gBACjD1C,OAAA;UAAKyC,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBAEpD1C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAOgD,OAAO,EAAC,cAAc;cAACP,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEvF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cACEiD,IAAI,EAAC,MAAM;cACXhB,EAAE,EAAC,cAAc;cACjBd,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAEjB,QAAQ,CAACE,YAAa;cAC7B6C,QAAQ,EAAEjC,iBAAkB;cAC5BwB,SAAS,EAAE,2FACT/B,MAAM,CAACL,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,EACzD;cACH8C,WAAW,EAAC;YAAqB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClC,CAAC,EACDpC,MAAM,CAACL,YAAY,iBAClBL,OAAA;cAAGyC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEhC,MAAM,CAACL;YAAY;cAAAsC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAClE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN9C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAOgD,OAAO,EAAC,aAAa;cAACP,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEtF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cACEiD,IAAI,EAAC,MAAM;cACXhB,EAAE,EAAC,aAAa;cAChBd,IAAI,EAAC,aAAa;cAClBC,KAAK,EAAEjB,QAAQ,CAACG,WAAY;cAC5B4C,QAAQ,EAAEjC,iBAAkB;cAC5BwB,SAAS,EAAE,2FACT/B,MAAM,CAACJ,WAAW,GAAG,gBAAgB,GAAG,iBAAiB,EACxD;cACH6C,WAAW,EAAC;YAAoB;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,EACDpC,MAAM,CAACJ,WAAW,iBACjBN,OAAA;cAAGyC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEhC,MAAM,CAACJ;YAAW;cAAAqC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CACjE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC,eAGN9C,OAAA;YAAA0C,QAAA,gBACE1C,OAAA;cAAOgD,OAAO,EAAC,cAAc;cAACP,SAAS,EAAC,8CAA8C;cAAAC,QAAA,EAAC;YAEvF;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eACR9C,OAAA;cACEiD,IAAI,EAAC,QAAQ;cACbhB,EAAE,EAAC,cAAc;cACjBd,IAAI,EAAC,cAAc;cACnBC,KAAK,EAAEjB,QAAQ,CAACI,YAAa;cAC7B2C,QAAQ,EAAEjC,iBAAkB;cAC5BmC,IAAI,EAAC,MAAM;cACXC,GAAG,EAAC,GAAG;cACPZ,SAAS,EAAE,2FACT/B,MAAM,CAACH,YAAY,GAAG,gBAAgB,GAAG,iBAAiB,EACzD;cACH4C,WAAW,EAAC;YAAM;cAAAR,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnB,CAAC,EACDpC,MAAM,CAACH,YAAY,iBAClBP,OAAA;cAAGyC,SAAS,EAAC,2BAA2B;cAAAC,QAAA,EAAEhC,MAAM,CAACH;YAAY;cAAAoC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAClE;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGN9C,OAAA;UAAKyC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,eAC/B1C,OAAA;YACEiD,IAAI,EAAC,QAAQ;YACbR,SAAS,EAAC,qJAAqJ;YAAAC,QAAA,EAChK;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ,CAAC,EAGLtC,OAAO,CAACqB,MAAM,GAAG,CAAC,iBACjB7B,OAAA;MAAKyC,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAChD1C,OAAA;QAAIyC,SAAS,EAAC,0CAA0C;QAAAC,QAAA,EAAC;MAAc;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAI,CAAC,eAE5E9C,OAAA;QAAKyC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,eAC9B1C,OAAA;UAAOyC,SAAS,EAAC,mBAAmB;UAAAC,QAAA,gBAClC1C,OAAA;YAAA0C,QAAA,eACE1C,OAAA;cAAIyC,SAAS,EAAC,YAAY;cAAAC,QAAA,gBACxB1C,OAAA;gBAAIyC,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAAI;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAC/E9C,OAAA;gBAAIyC,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAAQ;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACnF9C,OAAA;gBAAIyC,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAAO;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClF9C,OAAA;gBAAIyC,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAAK;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAChF9C,OAAA;gBAAIyC,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAAM;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/E;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA,CAAC,eACR9C,OAAA;YAAA0C,QAAA,EACGlC,OAAO,CAAC8C,GAAG,CAAEvC,IAAI,iBAChBf,OAAA;cAAkByC,SAAS,EAAC,2CAA2C;cAAAC,QAAA,gBACrE1C,OAAA;gBAAIyC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAE3B,IAAI,CAACsB;cAAI;gBAAAM,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eAChE9C,OAAA;gBAAIyC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAE3B,IAAI,CAACV;cAAY;gBAAAsC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACxE9C,OAAA;gBAAIyC,SAAS,EAAC,iCAAiC;gBAAAC,QAAA,EAAE3B,IAAI,CAACT;cAAW;gBAAAqC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACvE9C,OAAA;gBAAIyC,SAAS,EAAC,6CAA6C;gBAAAC,QAAA,GAAC,GAAC,EAAC3B,IAAI,CAACR,YAAY;cAAA;gBAAAoC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAK,CAAC,eACrF9C,OAAA;gBAAIyC,SAAS,EAAC,WAAW;gBAAAC,QAAA,eACvB1C,OAAA;kBACEuD,OAAO,EAAEA,CAAA,KAAMhB,YAAY,CAACxB,IAAI,CAACkB,EAAE,CAAE;kBACrCQ,SAAS,EAAC,qDAAqD;kBAAAC,QAAA,EAChE;gBAED;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAQ;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC;YAAA,GAZE/B,IAAI,CAACkB,EAAE;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAaZ,CACL;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAAA5C,EAAA,CApNKD,UAAU;AAAAuD,EAAA,GAAVvD,UAAU;AAsNhB,eAAeA,UAAU;AAAA,IAAAuD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}